<template>
  <!-- Password Vault Modal -->
  <Transition name="modal-fade">
    <div
      v-if="show"
      class="password-vault-overlay"
      @click.self="$emit('close')"
    >
      <Transition name="vault-bounce">
        <div v-if="show" class="password-vault-container">
          <!-- Vault particles -->
          <div class="vault-particles">
            <div v-for="i in 20" :key="i" class="vault-particle" />
          </div>

          <!-- Table CRUD Content -->
          <div class="table-content">
            <div class="table-header">
              <h2 class="table-title">
                <Database class="title-icon" />
                数据管理中心
              </h2>
              <div class="search-add-container">
                <el-input
                  v-model="searchText"
                  placeholder="搜索用户..."
                  class="search-input"
                  :prefix-icon="Search"
                  @input="handleSearch"
                />
                <el-button
                  type="primary"
                  class="add-btn"
                  @click="handleAdd"
                  :icon="Plus"
                >
                  添加用户
                </el-button>
              </div>
            </div>

            <!-- Table -->
            <div class="table-wrapper">
              <el-table
                :data="filteredTableData"
                class="custom-table"
                stripe
                border
                style="width: 100%"
                :header-cell-style="{
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: '#fff',
                  fontWeight: 'bold'
                }"
                :cell-style="{
                  background: 'rgba(255, 255, 255, 0.05)',
                  color: '#fff',
                  border: '1px solid rgba(255, 255, 255, 0.1)'
                }"
              >
                <el-table-column prop="id" label="ID" width="80" align="center" />
                <el-table-column prop="name" label="姓名" />
                <el-table-column prop="email" label="邮箱" />
                <el-table-column prop="department" label="部门" />
                <el-table-column prop="position" label="职位" />
                <!-- <el-table-column prop="status" label="状态" width="100" align="center">
                  <template #default="scope">
                    <el-tag
                      :type="scope.row.status === '在职' ? 'success' : 'danger'"
                      class="status-tag"
                    >
                      {{ scope.row.status }}
                    </el-tag>
                  </template>
                </el-table-column> -->
                <el-table-column prop="joinDate" label="入职日期" />
                <el-table-column label="操作" align="center" fixed="right">
                  <template #default="scope">
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleEdit(scope.row)"
                      :icon="Edit"
                      circle
                      class="action-btn"
                    />
                    <el-button
                      type="danger"
                      size="small"
                      @click="handleDelete(scope.row)"
                      :icon="Trash2"
                      circle
                      class="action-btn"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="totalData"
                layout="total, sizes, prev, pager, next, jumper"
                class="custom-pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>

          <!-- Add/Edit Dialog -->
          <el-dialog
            v-model="dialogVisible"
            :title="dialogTitle"
            width="500px"
            class="custom-dialog"
            :before-close="handleDialogClose"
          >
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="80px"
              class="dialog-form"
            >
              <el-form-item label="姓名" prop="name">
                <el-input v-model="formData.name" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="formData.email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item label="部门" prop="department">
                <el-select v-model="formData.department" placeholder="请选择部门" style="width: 100%">
                  <el-option label="技术部" value="技术部" />
                  <el-option label="产品部" value="产品部" />
                  <el-option label="运营部" value="运营部" />
                  <el-option label="市场部" value="市场部" />
                  <el-option label="人力资源" value="人力资源" />
                </el-select>
              </el-form-item>
              <el-form-item label="职位" prop="position">
                <el-input v-model="formData.position" placeholder="请输入职位" />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formData.status">
                  <el-radio value="在职">在职</el-radio>
                  <el-radio value="离职">离职</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="入职日期" prop="joinDate">
                <el-date-picker
                  v-model="formData.joinDate"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-form>
            <template #footer>
              <el-button @click="handleDialogClose">取消</el-button>
              <el-button type="primary" @click="handleSave" :loading="saveLoading">
                {{ editingId ? '更新' : '保存' }}
              </el-button>
            </template>
          </el-dialog>

          <!-- Close button -->
          <button @click="$emit('close')" class="vault-close-btn">
            <X class="vault-icon" />
          </button>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import { X, Database, Search, Plus, Edit, Trash2 } from "lucide-vue-next";
import { ElMessage, ElMessageBox } from "element-plus";

// Props
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

// Emits
defineEmits(["close"]);

// Table data
const tableData = ref([
  {
    id: 1,
    name: "张三",
    email: "<EMAIL>",
    department: "技术部",
    position: "前端开发工程师",
    status: "在职",
    joinDate: "2023-01-15"
  },
  {
    id: 2,
    name: "李四",
    email: "<EMAIL>",
    department: "产品部",
    position: "产品经理",
    status: "在职",
    joinDate: "2023-03-20"
  },
  {
    id: 3,
    name: "王五",
    email: "<EMAIL>",
    department: "技术部",
    position: "后端开发工程师",
    status: "在职",
    joinDate: "2022-11-10"
  },
  {
    id: 4,
    name: "赵六",
    email: "<EMAIL>",
    department: "运营部",
    position: "运营专员",
    status: "离职",
    joinDate: "2022-08-05"
  },
  {
    id: 5,
    name: "钱七",
    email: "<EMAIL>",
    department: "市场部",
    position: "市场推广",
    status: "在职",
    joinDate: "2023-05-12"
  },
  {
    id: 6,
    name: "孙八",
    email: "<EMAIL>",
    department: "人力资源",
    position: "HR专员",
    status: "在职",
    joinDate: "2023-02-28"
  }
]);

// Search and pagination
const searchText = ref("");
const currentPage = ref(1);
const pageSize = ref(10);

// Computed properties
const filteredTableData = computed(() => {
  let filtered = tableData.value;
  if (searchText.value) {
    filtered = filtered.filter(item =>
      item.name.includes(searchText.value) ||
      item.email.includes(searchText.value) ||
      item.department.includes(searchText.value) ||
      item.position.includes(searchText.value)
    );
  }

  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filtered.slice(start, end);
});

const totalData = computed(() => {
  if (searchText.value) {
    return tableData.value.filter(item =>
      item.name.includes(searchText.value) ||
      item.email.includes(searchText.value) ||
      item.department.includes(searchText.value) ||
      item.position.includes(searchText.value)
    ).length;
  }
  return tableData.value.length;
});

// Dialog
const dialogVisible = ref(false);
const dialogTitle = computed(() => editingId.value ? '编辑用户' : '添加用户');
const editingId = ref(null);
const saveLoading = ref(false);

// Form
const formRef = ref();
const formData = reactive({
  name: "",
  email: "",
  department: "",
  position: "",
  status: "在职",
  joinDate: ""
});

const formRules = {
  name: [
    { required: true, message: "请输入姓名", trigger: "blur" }
  ],
  email: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" }
  ],
  department: [
    { required: true, message: "请选择部门", trigger: "change" }
  ],
  position: [
    { required: true, message: "请输入职位", trigger: "blur" }
  ],
  joinDate: [
    { required: true, message: "请选择入职日期", trigger: "change" }
  ]
};

// Methods
const handleSearch = () => {
  currentPage.value = 1;
};

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
};

const handleAdd = () => {
  resetForm();
  editingId.value = null;
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  editingId.value = row.id;
  Object.assign(formData, { ...row });
  dialogVisible.value = true;
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const index = tableData.value.findIndex(item => item.id === row.id);
    if (index > -1) {
      tableData.value.splice(index, 1);
      ElMessage.success('删除成功');
    }
  } catch {
    ElMessage.info('已取消删除');
  }
};

const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saveLoading.value = true;

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (editingId.value) {
      // Edit existing
      const index = tableData.value.findIndex(item => item.id === editingId.value);
      if (index > -1) {
        tableData.value[index] = { ...formData, id: editingId.value };
        ElMessage.success('更新成功');
      }
    } else {
      // Add new
      const newId = Math.max(...tableData.value.map(item => item.id)) + 1;
      tableData.value.push({ ...formData, id: newId });
      ElMessage.success('添加成功');
    }

    dialogVisible.value = false;
    resetForm();
  } catch (error) {
    console.error('Validation failed:', error);
  } finally {
    saveLoading.value = false;
  }
};

const handleDialogClose = () => {
  dialogVisible.value = false;
  resetForm();
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(formData, {
    name: "",
    email: "",
    department: "",
    position: "",
    status: "在职",
    joinDate: ""
  });
  editingId.value = null;
};
</script>

<style scoped>
/* Modal transitions */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.vault-bounce-enter-active {
  animation: vault-bounce-in 0.5s ease-out;
}

.vault-bounce-leave-active {
  animation: vault-bounce-out 0.3s ease-in;
}

@keyframes vault-bounce-in {
  0% {
    transform: scale(0.8) translateY(50px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) translateY(-10px);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes vault-bounce-out {
  0% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  100% {
    transform: scale(0.8) translateY(30px);
    opacity: 0;
  }
}

/* Password Vault Modal Styles */
.password-vault-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-vault-container {
  width: 95%;
  max-width: 1400px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  padding: 20px;
}

/* Particles */
.vault-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.vault-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.vault-particle:nth-child(odd) {
  animation-delay: -2s;
}

.vault-particle:nth-child(even) {
  animation-delay: -4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
}

/* Generate random positions for particles */
.vault-particle:nth-child(1) { top: 10%; left: 10%; }
.vault-particle:nth-child(2) { top: 20%; left: 80%; }
.vault-particle:nth-child(3) { top: 30%; left: 30%; }
.vault-particle:nth-child(4) { top: 40%; left: 70%; }
.vault-particle:nth-child(5) { top: 50%; left: 20%; }
.vault-particle:nth-child(6) { top: 60%; left: 90%; }
.vault-particle:nth-child(7) { top: 70%; left: 40%; }
.vault-particle:nth-child(8) { top: 80%; left: 60%; }
.vault-particle:nth-child(9) { top: 15%; left: 50%; }
.vault-particle:nth-child(10) { top: 25%; left: 15%; }
.vault-particle:nth-child(11) { top: 35%; left: 85%; }
.vault-particle:nth-child(12) { top: 45%; left: 25%; }
.vault-particle:nth-child(13) { top: 55%; left: 75%; }
.vault-particle:nth-child(14) { top: 65%; left: 35%; }
.vault-particle:nth-child(15) { top: 75%; left: 95%; }
.vault-particle:nth-child(16) { top: 85%; left: 45%; }
.vault-particle:nth-child(17) { top: 5%; left: 65%; }
.vault-particle:nth-child(18) { top: 95%; left: 25%; }
.vault-particle:nth-child(19) { top: 12%; left: 88%; }
.vault-particle:nth-child(20) { top: 88%; left: 12%; }

/* Table content */
.table-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  z-index: 10;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 20px;
}

.table-title {
  color: white;
  font-size: 24px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

::v-deep(.el-table tr) {
  background-color: transparent !important;
}

.title-icon {
  width: 28px;
  height: 28px;
  color: #64ffda;
}

.search-add-container {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
  margin-right: 70px;
}

.search-input {
  width: 300px;
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-border-color: rgba(255, 255, 255, 0.2);
  --el-input-hover-border-color: rgba(100, 255, 218, 0.6);
  --el-input-focus-border-color: #64ffda;
  --el-input-text-color: white;
  --el-input-placeholder-color: rgba(255, 255, 255, 0.6);
}

.add-btn {
  --el-button-bg-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --el-button-border-color: transparent;
  --el-button-hover-bg-color: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

/* Table wrapper */
.table-wrapper {
  flex: 1;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  /* border: 1px solid rgba(255, 255, 255, 0.1); */
  overflow: auto;
}

/* Custom table styles */
:deep(.custom-table) {
  background: transparent !important;
  /* border: 1px solid rgba(255, 255, 255, 0.1) !important; */
}

:deep(.custom-table .el-table__header-wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
}

:deep(.custom-table .el-table__body-wrapper) {
  background: transparent !important;
}

:deep(.custom-table .el-table__row) {
  background: rgba(255, 255, 255, 0.02) !important;
}

:deep(.custom-table .el-table__row:hover) {
  background: rgba(100, 255, 218, 0.1) !important;
}

:deep(.custom-table .el-table__cell) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-table__inner-wrapper:before) {
  background-color: transparent !important;
}
:deep(.el-table--border) {
  background-color: transparent !important;
}
:deep(.el-table__inner-wrapper:after) {
  background-color: transparent !important;
}
:deep(.el-table--border:after) {
  background-color: transparent !important;
}
:deep(.el-table--border:before) {
  background-color: transparent !important;
}

:deep(.el-table) {
  --el-table-border-color: none !important;
}
:deep(.el-pagination__total),
:deep(.el-pagination__jump) {
  color: white !important;
}

.status-tag {
  font-weight: bold;
}

.action-btn {
  margin: 0 2px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Pagination */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

:deep(.custom-pagination) {
  --el-pagination-bg-color: rgba(255, 255, 255, 0.1);
  --el-pagination-text-color: white;
  --el-pagination-border-radius: 8px;
}

:deep(.custom-pagination .el-pagination__editor.el-input) {
  --el-input-bg-color: rgba(255, 255, 255, 0.1);
  --el-input-border-color: rgba(255, 255, 255, 0.2);
  --el-input-text-color: white;
}

:deep(.custom-pagination .el-select) {
  --el-select-input-color: white;
  --el-select-input-focus-border-color: #64ffda;
}

:deep(.custom-pagination .btn-prev),
:deep(.custom-pagination .btn-next) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

:deep(.custom-pagination .number) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

:deep(.custom-pagination .number.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-color: transparent !important;
}

/* Dialog styles */
:deep(.custom-dialog) {
  --el-dialog-bg-color: rgba(15, 12, 41, 0.2);
  --el-dialog-border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

:deep(.custom-dialog .el-dialog__header) {
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  color: white;
  border-radius: 8px 8px 0 0;
  padding: 20px;
}

:deep(.custom-dialog .el-dialog__title) {
  color: white;
  font-weight: bold;
}

:deep(.custom-dialog .el-dialog__body) {
  /* background: rgba(15, 12, 41, 0.2); */
  color: white;
  padding: 30px;
}

:deep(.custom-dialog .el-dialog__footer) {
  /* background: rgba(15, 12, 41, 0.2); */
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 30px;
}

.dialog-form :deep(.el-form-item__label) {
  color: white !important;
}

.dialog-form :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

.dialog-form :deep(.el-input__inner) {
  color: white !important;
}

.dialog-form :deep(.el-select__wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.dialog-form :deep(.el-select__placeholder) {
  color: rgba(255, 255, 255, 0.6) !important;
}

.dialog-form :deep(.el-radio__label) {
  color: white !important;
}

.dialog-form :deep(.el-date-editor.el-input) {
  --el-input-bg-color: rgba(255, 255, 255, 0.1) !important;
  --el-input-border-color: rgba(255, 255, 255, 0.2) !important;
  --el-input-text-color: white !important;
}

/* Close button */
.vault-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  /* width: 40px; */
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
}

.vault-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.vault-icon {
  width: 20px;
  height: 20px;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .password-vault-container {
    width: 98%;
    height: 95vh;
    border-radius: 12px;
    padding: 15px;
  }

  .table-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-add-container {
    justify-content: space-between;
  }

  .search-input {
    width: 200px;
  }

  .table-title {
    font-size: 20px;
    text-align: center;
  }
}
</style>
