import Dexie from 'dexie';

// 定义数据库类
class AppDB extends <PERSON><PERSON> {
  constructor() {
    super('UserManagementDB');
    
    // 定义数据库版本和表结构
    this.version(1).stores({
      users: '++id, name, email, status, createdAt' // 主键是自增id
    });
    
    this.users = this.table('users');
  }
}

// 创建数据库实例
const db = new AppDB();

// 导出数据库操作方法
export const userDB = {
  // 添加用户
  async addUser(user) {
    return await db.users.add({
      ...user,
      createdAt: new Date()
    });
  },
  
  // 更新用户
  async updateUser(id, user) {
    return await db.users.update(id, user);
  },
  
  // 删除用户
  async deleteUser(id) {
    return await db.users.delete(id);
  },
  
  // 获取所有用户
  async getAllUsers() {
    return await db.users.toArray();
  },
  
  // 根据ID获取用户
  async getUserById(id) {
    return await db.users.get(id);
  },
  
  // 搜索用户
  async searchUsers(keyword) {
    return await db.users
      .filter(user => 
        user.name.includes(keyword) || 
        user.email.includes(keyword)
      )
      .toArray();
  }
};

export default db;
