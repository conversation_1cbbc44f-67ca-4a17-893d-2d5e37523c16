<template>
  <div class="desktop-container">
    <!-- Dynamic background with animated elements -->
    <div class="background">
      <div class="particles"></div>
    </div>

    <!-- Main content -->
    <div class="desktop-content">
      <!-- Top section with time and date -->
      <div class="top-section">
        <div class="time-display">
          <div class="time">{{ currentTime }}</div>
          <div class="date">{{ currentDate }}</div>
        </div>

        <!-- Enhanced search box -->
        <div class="search-container">
          <div class="search-box">
            <Search class="search-icon" />
            <input
              type="text"
              placeholder="请输入搜索内容"
              class="search-input"
              v-model="searchQuery"
              @keyup.enter="performSearch"
            />
            <select v-model="selectedEngine" class="search-engine-select">
              <option value="https://www.baidu.com/s?wd=">百度</option>
              <option value="https://www.bing.com/search?q=">必应</option>
              <option value="https://www.sogou.com/web?query=">搜狗</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Dock with glass morphism effect -->
      <div class="dock-wrapper">
        <div class="dock">
          <div
            v-for="dockApp in dockApps"
            :key="dockApp.name"
            @click="openApp(dockApp)"
            class="dock-item"
            :class="{ pulse: dockApp.highlight }"
          >
            <div class="dock-icon" :style="{ background: dockApp.color }">
              <component :is="dockApp.icon" class="dock-icon-svg" />
            </div>
            <div class="dock-tooltip">{{ dockApp.name }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Password Vault Modal -->
    <PasswordVault :show="showPasswordVault" @close="closePasswordVault" />
    <!-- Memo Modal -->
    <MemoModal :show="showMemo" @close="closeMemo" />
    <!-- Health Modal -->
    <HealthModal :show="showHealth" @close="closeHealth" />
    <FoodMall :show="showFoodMall" @close="closeFoodMall" />
    <AdminMain :show="showAdminMain" @close="closeAdminMain" @openUserManagement="openAFoodMall"/> />
    <AFoodMall :show="showAFoodMall" @close="closeAFoodMall" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import {
  Search,
  Play,
  Archive,
  UtensilsCrossed,
  MessageCircle,
  Zap,
  Star,
  Cloud,
  Compass,
  Coffee,
  Code,
  Cpu,
  StickyNote,
  Heart,
  Shield,
  Smile,
  User,
} from "lucide-vue-next";

import { ElMessage, ElMessageBox } from "element-plus";

// Import components
import MemoModal from "./MemoModal.vue";
import PasswordVault from "./PasswordVault.vue";
import HealthModal from "./HealthModal.vue";
import FoodMall from "./FoodMall.vue";
import AFoodMall from "../admin/AFoodMall.vue";

// admin管理端
import AdminMain from "../admin/Main.vue";

// Reactive data
const currentTime = ref("");
const currentDate = ref("");
const selectedEngine = ref("https://www.baidu.com/s?wd=");
const searchQuery = ref("");
const showMemo = ref(false);
const showHealth = ref(false);
const showPasswordVault = ref(false);
const showFoodMall = ref(false);

const showAdminMain = ref(false);
const showAFoodMall = ref(false);

// Dock applications with enhanced styling and icons
const dockApps = ref([
  {
    name: "思盒OA",
    icon: Coffee,
    color: "linear-gradient(135deg, #00d4aa, #00a884)",
    path: "http://192.168.28.8/hsoftoa/#/login?redirect=%2Fdashboard",
    highlight: true,
  },
  {
    name: "备忘录",
    icon: StickyNote,
    color: "linear-gradient(135deg, #ff9a9e, #fad0c4)",
    action: "openMemo",
    highlight: false,
  },
  {
    name: "密码库",
    icon: Shield,
    color: "linear-gradient(135deg, #667eea, #764ba2)",
    action: "openPasswordVault",
    highlight: false,
  },
  {
    name: "运动健康",
    icon: Heart,
    color: "linear-gradient(135deg, #ff6b6b, #ee5a52)",
    action: "openHealth",
    highlight: false,
  },
  {
    name: "美食MALL",
    icon: UtensilsCrossed,
    color: "linear-gradient(135deg, #55a3ff, #2e86de)",
    action: "openFoodMall",
    highlight: false,
  },
  {
    name: "管理端",
    icon: User,
    color: "linear-gradient(135deg, #00b894, #00cec9)",
    action: "openAdminMain",
    highlight: false,
  },
]);

// Search function
function performSearch() {
  if (searchQuery.value.trim()) {
    window.open(
      selectedEngine.value + encodeURIComponent(searchQuery.value),
      "_blank"
    );
    searchQuery.value = "";
  }
}

// Update time function
const updateTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  currentTime.value = `${hours}:${minutes}`;

  const options = {
    month: "long",
    day: "numeric",
    weekday: "long",
  };
  currentDate.value = now.toLocaleDateString("zh-CN", options) + " 五月初三";
};

// Open application function
const openApp = (app) => {
  if (app.action === "openMemo") {
    openMemo();
  } else if (app.action === "openHealth") {
    openHealth();
  } else if (app.action === "openPasswordVault") {
    openPasswordVault();
  } else if (app.action === "openFoodMall") {
    openFoodMall();
  } else if (app.action === "openParticleImage") {
    openParticleImage();
  } else if (app.action === "openAdminMain") {
    openAdminMain();
  } else if (app.action === "openAFoodMall") {
    openAFoodMall();
  } else if (app.path) {
    window.open(app.path, "_blank");
  }
};

// Modal functions
const openPasswordVault = () => {
  showPasswordVault.value = true;
};

const closePasswordVault = () => {
  showPasswordVault.value = false;
};


const openMemo = () => {
  showMemo.value = true;
};

const closeMemo = () => {
  showMemo.value = false;
};

const openHealth = () => {
  showHealth.value = true;
};

const closeHealth = () => {
  showHealth.value = false;
};
const openFoodMall = () => {
  showFoodMall.value = true;
};
const closeFoodMall = () => {
  showFoodMall.value = false;
};
const openAFoodMall = () => {
  showAFoodMall.value = true;
};
const closeAFoodMall = () => {
  showAFoodMall.value = false;
};
const openAdminMain = () => {
  // showParticleImage.value = true;
  ElMessageBox.prompt("请输入唯一KEY", "KEY", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    // inputPattern:
    //   /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
    // inputErrorMessage: 'Invalid Email',
  })
    .then(({ value }) => {
      showAdminMain.value = true;

      console.log(value, "306---");
      if (value == "zhangzuying") {
        showAdminMain.value = true;
      } else {
        ElMessage({
          type: "info",
          message: "口令错误，无法进入管理端！",
        });
      }
      // ElMessage({
      //   type: 'success',
      //   message: `Your email is:${value}`,
      // })
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "Input canceled",
      });
    });
};
const closeAdminMain = () => {
  showAdminMain.value = false;
};

// Set up time interval
let timeInterval;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
  createParticles();
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

// Function to create particle effect
const createParticles = () => {
  const particlesContainer = document.querySelector(".particles");
  if (!particlesContainer) return;

  for (let i = 0; i < 50; i++) {
    const particle = document.createElement("div");
    particle.classList.add("particle");
    particle.style.left = Math.random() * 100 + "vw";
    particle.style.top = Math.random() * 100 + "vh";
    const size = Math.random() * 5 + 2;
    particle.style.width = size + "px";
    particle.style.height = size + "px";
    particle.style.animationDelay = Math.random() * 5 + "s";
    particle.style.animationDuration = Math.random() * 10 + 10 + "s";
    particlesContainer.appendChild(particle);
  }
};
</script>

<style scoped>
/* Base container */
.desktop-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  color: white;
}

/* Enhanced background with more vibrant gradients */
.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background: linear-gradient(135deg, #0f0c29, #302b63, #24243e); */
  overflow: hidden;
  background-image: url("../assets/bg8.jpg");
  background-size: cover;
}

/* Particles effect */
.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.particle {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: rise linear infinite;
}

@keyframes rise {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(20px);
    opacity: 0;
  }
}

/* Main content */
.desktop-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Top section with time and search */
.top-section {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 40px;
}

/* Time display with enhanced typography */
.time-display {
  margin-bottom: 30px;
}

.time {
  font-size: 5rem;
  font-weight: 200;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  margin-bottom: 5px;
  letter-spacing: 2px;
}

.date {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  letter-spacing: 1px;
}

/* Enhanced search container */
.search-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

/* Glass morphism search box */
.search-box {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 8px 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 600px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.search-box:hover,
.search-box:focus-within {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.search-icon {
  margin-right: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* Transparent input field */
.search-input {
  border: none;
  outline: none;
  flex: 1;
  font-size: 16px;
  padding: 10px 0;
  margin-right: 12px;
  background: transparent;
  color: white;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Stylish select dropdown */
.search-engine-select {
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 8px 16px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  position: relative;
}

.search-engine-select:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.search-engine-select option {
  background: #302b63;
  color: white;
}

/* Dock wrapper for positioning */
.dock-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  padding-bottom: 60px;
}

/* Enhanced dock with glass morphism */
.dock {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  flex-wrap: wrap;
}

.dock:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Enhanced dock items with hover effects */
.dock-item {
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.dock-item:hover {
  transform: translateY(-12px) scale(1.1);
}

/* Dock icons with gradient backgrounds */
.dock-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dock-icon::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0)
  );
  border-radius: 14px;
}

.dock-icon-svg {
  width: 28px;
  height: 28px;
  color: white;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
  z-index: 1;
}

/* Tooltip that appears on hover */
.dock-tooltip {
  position: absolute;
  top: -40px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 6px;
  font-size: 12px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
}

.dock-item:hover .dock-tooltip {
  opacity: 1;
  transform: translateY(0);
}

/* Pulse animation for highlighted items */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}
</style>